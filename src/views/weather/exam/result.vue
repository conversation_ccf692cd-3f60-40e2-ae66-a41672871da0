<template>
  <div class="weather-exam-result-container">
    <!-- 页面头部 -->
    <div class="result-header">
      <div class="header-content">
        <div class="header-icon">
          <i class="el-icon-trophy" />
        </div>
        <div class="header-text">
          <h1 class="page-title">考试结果</h1>
          <p class="page-subtitle">{{ examData.title }}</p>
        </div>
      </div>
      <div class="back-button">
        <el-button type="primary" plain @click="goBack">
          <i class="el-icon-back" />
          返回
        </el-button>
      </div>
    </div>

    <!-- 考试信息卡片 -->
    <div v-loading="loading" class="exam-info-section">
      <div class="info-card">
        <div class="score-display">
          <div class="score-circle">
            <div class="score-number">{{ userScore }}</div>
            <div class="score-label">分</div>
          </div>
          <div class="score-details">
            <div class="detail-item">
              <span class="label">总分：</span>
              <span class="value">{{ examData.totalScore || 100 }}分</span>
            </div>
            <div class="detail-item">
              <span class="label">用时：</span>
              <span class="value">{{ formatTime(userTime) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">提交时间：</span>
              <span class="value">{{ formatDateTime(submitTime) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 题目详情 -->
    <div v-if="questionData" class="question-section">
      <div class="question-card">
        <div class="question-header">
          <h3>题目详情</h3>
          <el-tag type="success" size="small">已完成</el-tag>
        </div>

        <div class="question-content">
          <div class="question-title">{{ questionData.title }}</div>
          <div v-if="questionData.content" class="question-description">
            {{ questionData.content }}
          </div>

          <!-- 天气预报表格 -->
          <div v-if="forecastData" class="forecast-table-section">
            <h4>天气预报表格</h4>
            <WeatherForecastTable
              :forecast-data="forecastData"
              :stations="stations"
              :readonly="true"
              :show-correct-answers="true"
              :user-answers="userAnswers"
            />
          </div>

          <!-- 正确答案显示 -->
          <div v-if="questionData.answerList" class="correct-answers-section">
            <h4>正确答案</h4>
            <div class="answers-grid">
              <div
                v-for="answer in questionData.answerList"
                :key="answer.id"
                :class="['answer-item', { 'correct-answer': answer.isRight }]"
              >
                <div class="answer-content">{{ answer.content }}</div>
                <div v-if="answer.isRight" class="correct-badge">
                  <i class="el-icon-check" />
                  正确答案
                </div>
                <div v-if="answer.analysis" class="answer-analysis">
                  <strong>解析：</strong>{{ answer.analysis }}
                </div>
              </div>
            </div>
          </div>

          <!-- 详细评分信息 -->
          <div v-if="gradingDetails && gradingDetails.detailResults" class="grading-details-section">
            <h4>详细评分信息</h4>

            <!-- 降水分级落区预报 -->
            <div v-if="gradingDetails.detailResults.precipitationArea" class="precipitation-area-section">
              <div class="section-header">
                <h5>第一部分 - 降水分级落区预报</h5>
                <div class="section-score">
                  得分：{{ formatScore(gradingDetails.detailResults.precipitationArea.score) }}分
                </div>
              </div>

              <div class="precipitation-details">
                <!-- 基础得分 -->
                <div v-if="gradingDetails.detailResults.precipitationArea.baseScores" class="score-group">
                  <h6>基础得分 (baseScores)</h6>
                  <div class="score-items">
                    <div
                      v-for="level in getSortedPrecipitationLevels(gradingDetails.detailResults.precipitationArea.baseScores)"
                      :key="'base-' + level"
                      class="score-item"
                    >
                      <span class="score-label">{{ level }}：</span>
                      <span class="score-value">{{ formatScore(gradingDetails.detailResults.precipitationArea.baseScores[level]) }}</span>
                    </div>
                  </div>
                </div>

                <!-- 学生TS评分 -->
                <div v-if="gradingDetails.detailResults.precipitationArea.studentTSScores" class="score-group">
                  <h6>学生TS评分 (studentTSScores)</h6>
                  <div class="score-items">
                    <div
                      v-for="level in getSortedPrecipitationLevels(gradingDetails.detailResults.precipitationArea.studentTSScores)"
                      :key="'student-' + level"
                      class="score-item"
                    >
                      <span class="score-label">{{ level }}：</span>
                      <span class="score-value">{{ formatScore(gradingDetails.detailResults.precipitationArea.studentTSScores[level]) }}</span>
                    </div>
                  </div>
                </div>

                <!-- 中央气象台中尺度TS评分 -->
                <div v-if="gradingDetails.detailResults.precipitationArea.cmaMesoTSScores" class="score-group">
                  <h6>中央气象台中尺度TS评分 (cmaMesoTSScores)</h6>
                  <div class="score-items">
                    <div
                      v-for="level in getSortedPrecipitationLevels(gradingDetails.detailResults.precipitationArea.cmaMesoTSScores)"
                      :key="'cma-' + level"
                      class="score-item"
                    >
                      <span class="score-label">{{ level }}：</span>
                      <span class="score-value">{{ formatScore(gradingDetails.detailResults.precipitationArea.cmaMesoTSScores[level]) }}</span>
                    </div>
                  </div>
                </div>

                <!-- 技能评分 -->
                <div v-if="gradingDetails.detailResults.precipitationArea.skillScores" class="score-group">
                  <h6>技能评分 (skillScores)</h6>
                  <div class="score-items">
                    <div
                      v-for="level in getSortedPrecipitationLevels(gradingDetails.detailResults.precipitationArea.skillScores)"
                      :key="'skill-' + level"
                      class="score-item"
                    >
                      <span class="score-label">{{ level }}：</span>
                      <span class="score-value">{{ formatScore(gradingDetails.detailResults.precipitationArea.skillScores[level]) }}</span>
                    </div>
                  </div>
                </div>

                <!-- TS评分详细统计 -->
                <div v-if="hasLevelTSDetails" class="ts-details-section">
                  <h6>TS评分详细统计</h6>
                  <div class="ts-details-content">
                    <div
                      v-for="levelDetail in getLevelTSDetails()"
                      :key="levelDetail.level"
                      class="level-ts-detail"
                    >
                      <div class="level-header">
                        <h7>{{ levelDetail.level }}量级</h7>
                        <div class="level-stats">
                          <span class="station-count">参与站点：{{ levelDetail.totalStations }}个</span>
                          <span v-if="levelDetail.specialRuleNote" class="special-rule" :title="levelDetail.specialRuleNote">
                            <i class="el-icon-info"></i>
                          </span>
                        </div>
                      </div>

                      <!-- 学生TS评分统计 -->
                      <div v-if="levelDetail.studentTSStats" class="ts-stats-group">
                        <div class="stats-header">
                          <span class="stats-title">学生TS评分</span>
                          <span class="ts-score">{{ formatTSScore(levelDetail.studentTSStats.tsScore) }}</span>
                        </div>
                        <div class="stats-breakdown">
                          <div class="stat-item correct">
                            <span class="stat-label">正确预报：</span>
                            <span class="stat-value">{{ levelDetail.studentTSStats.correctForecast }}站</span>
                          </div>
                          <div class="stat-item wrong">
                            <span class="stat-label">错误预报：</span>
                            <span class="stat-value">{{ levelDetail.studentTSStats.wrongForecast }}站</span>
                          </div>
                          <div class="stat-item missed">
                            <span class="stat-label">漏报站数：</span>
                            <span class="stat-value">{{ levelDetail.studentTSStats.missedForecast }}站</span>
                          </div>
                        </div>
                        <div v-if="levelDetail.studentTSStats.formulaDescription" class="formula-description">
                          <span class="formula-label">计算公式：</span>
                          <span class="formula-text">{{ levelDetail.studentTSStats.formulaDescription }}</span>
                        </div>
                      </div>

                      <!-- CMA-MESO TS评分统计 -->
                      <div v-if="levelDetail.cmaMesoTSStats" class="ts-stats-group cma-meso">
                        <div class="stats-header">
                          <span class="stats-title">CMA-MESO TS评分</span>
                          <span class="ts-score">{{ formatTSScore(levelDetail.cmaMesoTSStats.tsScore) }}</span>
                        </div>
                        <div class="stats-breakdown">
                          <div class="stat-item correct">
                            <span class="stat-label">正确预报：</span>
                            <span class="stat-value">{{ levelDetail.cmaMesoTSStats.correctForecast }}站</span>
                          </div>
                          <div class="stat-item wrong">
                            <span class="stat-label">错误预报：</span>
                            <span class="stat-value">{{ levelDetail.cmaMesoTSStats.wrongForecast }}站</span>
                          </div>
                          <div class="stat-item missed">
                            <span class="stat-label">漏报站数：</span>
                            <span class="stat-value">{{ levelDetail.cmaMesoTSStats.missedForecast }}站</span>
                          </div>
                        </div>
                        <div v-if="levelDetail.cmaMesoTSStats.formulaDescription" class="formula-description">
                          <span class="formula-label">计算公式：</span>
                          <span class="formula-text">{{ levelDetail.cmaMesoTSStats.formulaDescription }}</span>
                        </div>
                      </div>

                      <!-- 评分贡献信息 -->
                      <div v-if="levelDetail.contributionToFinalScore !== undefined" class="contribution-info">
                        <div class="contribution-item">
                          <span class="contribution-label">基础分：</span>
                          <span class="contribution-value">{{ formatScore(levelDetail.studentBaseScore) }}</span>
                        </div>
                        <div class="contribution-item">
                          <span class="contribution-label">技巧评分：</span>
                          <span class="contribution-value">{{ formatScore(levelDetail.studentSkillScore) }}</span>
                        </div>
                        <div class="contribution-item">
                          <span class="contribution-label">权重：</span>
                          <span class="contribution-value">{{ formatScore(levelDetail.weight) }}</span>
                        </div>
                        <div class="contribution-item total">
                          <span class="contribution-label">对最终得分贡献：</span>
                          <span class="contribution-value">{{ formatScore(levelDetail.contributionToFinalScore) }}分</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 总结信息 -->
                <div v-if="gradingDetails.detailResults.precipitationArea.summary" class="summary-section">
                  <h6>评分总结</h6>
                  <div class="summary-content">{{ gradingDetails.detailResults.precipitationArea.summary }}</div>
                </div>
              </div>
            </div>

            <!-- 其他评分项目 -->
            <div v-if="gradingDetails.detailResults.disasterWeather" class="disaster-weather-section">
              <div class="section-header">
                <h5>第二部分 - 灾害性天气预报</h5>
                <div class="section-score">
                  得分：{{ formatScore(gradingDetails.detailResults.disasterWeather.score) }}分
                </div>
              </div>
              <div class="disaster-details">
                <div class="detail-item">
                  <span class="label">匹配度：</span>
                  <span class="value">{{ gradingDetails.detailResults.disasterWeather.match ? '匹配' : '不匹配' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">差异：</span>
                  <span class="value">{{ gradingDetails.detailResults.disasterWeather.difference || 0 }}</span>
                </div>
                <div v-if="gradingDetails.detailResults.disasterWeather.reason" class="detail-item">
                  <span class="label">原因：</span>
                  <span class="value">{{ gradingDetails.detailResults.disasterWeather.reason }}</span>
                </div>
              </div>
            </div>

            <!-- 其他天气要素评分 -->
            <div v-for="(element, key) in getOtherWeatherElements()" :key="key" class="weather-element-section">
              <div class="section-header">
                <h5>{{ getElementDisplayName(key) }}</h5>
                <div class="section-score">
                  得分：{{ formatScore(element.score) }}分
                </div>
              </div>
              <div class="element-details">
                <div class="detail-item">
                  <span class="label">匹配度：</span>
                  <span class="value">{{ element.match ? '匹配' : '不匹配' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">差异：</span>
                  <span class="value">{{ element.difference || 0 }}</span>
                </div>
                <div v-if="element.reason" class="detail-item">
                  <span class="label">原因：</span>
                  <span class="value">{{ element.reason }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && !questionData" class="empty-state">
      <div class="empty-icon">
        <i class="el-icon-warning" />
      </div>
      <div class="empty-title">无法获取考试结果</div>
      <div class="empty-description">请确认您已参加并提交了该考试</div>
    </div>
  </div>
</template>

<script>
import WeatherForecastTable from '@/components/WeatherForecastTable'
import {
  getWeatherExamInfo,
  getWeatherCaseResultDetail,
  getWeatherExamAnswer
} from '@/api/weather/weather'

export default {
  name: 'WeatherExamResult',
  components: {
    WeatherForecastTable
  },
  data() {
    return {
      loading: false,
      examId: '',
      examData: {},
      questionData: null,
      userScore: 0,
      userTime: 0,
      submitTime: '',
      forecastData: null,
      stations: [],
      userAnswers: {},
      gradingDetails: null // 详细评分信息
    }
  },
  computed: {
    // 检查是否有TS评分详情数据
    hasLevelTSDetails() {
      return this.getLevelTSDetails().length > 0
    }
  },
  created() {
    this.examId = this.$route.params.id
    this.loadExamResult()
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 加载考试结果
    async loadExamResult() {
      this.loading = true
      try {
        // 1. 获取考试基本信息
        await this.loadExamInfo()

        // 2. 获取用户答案信息
        await this.loadUserAnswer()

        // 3. 获取题目详情（包含正确答案）
        await this.loadQuestionDetail()
      } catch (error) {
        console.error('加载考试结果失败:', error)
        this.$message.error('加载考试结果失败')
      } finally {
        this.loading = false
      }
    },

    // 加载考试信息
    async loadExamInfo() {
      const response = await getWeatherExamInfo({ id: this.examId })
      if (response.code === 0) {
        this.examData = response.data
      } else {
        throw new Error(response.msg || '获取考试信息失败')
      }
    },

    // 加载用户答案
    async loadUserAnswer() {
      const response = await getWeatherExamAnswer({ examId: this.examId })
      if (response.code === 0 && response.data) {
        const answerData = response.data
        this.userScore = answerData.totalScore || 0
        this.userTime = answerData.userTime || 0
        this.submitTime = answerData.submitTime || ''

        // 解析用户答案数据
        if (answerData.answerData) {
          try {
            this.userAnswers = JSON.parse(answerData.answerData)
          } catch (e) {
            console.warn('解析用户答案数据失败:', e)
          }
        }
      }
    },

    // 加载题目详情（需要验证参与状态）
    async loadQuestionDetail() {
      if (!this.examData.questionId) return

      const response = await getWeatherCaseResultDetail({
        examId: this.examId,
        questionId: this.examData.questionId
      })

      if (response.code === 0) {
        this.questionData = response.data

        // 解析预报数据
        if (this.questionData.scenarioData) {
          try {
            const scenarioData = JSON.parse(this.questionData.scenarioData)
            this.forecastData = scenarioData.forecastData || null
            this.stations = scenarioData.stations || []
          } catch (e) {
            console.warn('解析场景数据失败:', e)
          }
        }

        // 获取详细评分信息
        if (this.questionData.gradingDetails) {
          this.gradingDetails = this.questionData.gradingDetails
        }
      } else {
        throw new Error(response.msg || '获取题目详情失败')
      }
    },

    // 格式化时间（分钟）
    formatTime(minutes) {
      if (!minutes) return '0分钟'
      const hours = Math.floor(minutes / 60)
      const mins = minutes % 60
      if (hours > 0) {
        return `${hours}小时${mins}分钟`
      }
      return `${mins}分钟`
    },

    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    },

    // 格式化分数显示
    formatScore(score) {
      if (score === null || score === undefined) {
        return '0.00'
      }
      return Number(score).toFixed(2)
    },

    // 获取排序后的降水级别
    getSortedPrecipitationLevels(scoresObj) {
      if (!scoresObj || typeof scoresObj !== 'object') {
        return []
      }

      // 定义降水级别的显示顺序（从晴雨开始）
      const levelOrder = ['晴雨', '小雨', '中雨', '大雨', '暴雨', '大暴雨', '特大暴雨']

      // 获取对象中存在的级别，过滤掉微量降水
      const availableLevels = Object.keys(scoresObj).filter(level => level !== '微量降水')

      // 按照预定义顺序过滤和排序
      const sortedLevels = levelOrder.filter(level => availableLevels.includes(level))

      // 添加任何不在预定义顺序中但存在于数据中的级别（排除微量降水）
      const remainingLevels = availableLevels.filter(level => !levelOrder.includes(level) && level !== '微量降水')

      return [...sortedLevels, ...remainingLevels]
    },

    // 获取TS评分详情数据
    getLevelTSDetails() {
      if (!this.gradingDetails || !this.gradingDetails.detailResults) {
        return []
      }

      // 尝试从多个可能的路径获取levelTSDetails数据
      let levelTSDetails = null

      // 路径1: gradingDetails.detailResults.precipitationArea.levelTSDetails
      if (this.gradingDetails.detailResults.precipitationArea &&
          this.gradingDetails.detailResults.precipitationArea.levelTSDetails) {
        levelTSDetails = this.gradingDetails.detailResults.precipitationArea.levelTSDetails
      }

      // 路径2: gradingDetails.precipitationScoringDetails.levelTSDetails
      if (!levelTSDetails && this.gradingDetails.precipitationScoringDetails &&
          this.gradingDetails.precipitationScoringDetails.levelTSDetails) {
        levelTSDetails = this.gradingDetails.precipitationScoringDetails.levelTSDetails
      }

      // 路径3: gradingDetails.levelTSDetails
      if (!levelTSDetails && this.gradingDetails.levelTSDetails) {
        levelTSDetails = this.gradingDetails.levelTSDetails
      }

      if (!levelTSDetails || !Array.isArray(levelTSDetails)) {
        return []
      }

      // 按照降水量级排序
      const levelOrder = ['晴雨', '微量降水', '小雨', '中雨', '大雨', '暴雨', '大暴雨']
      return levelTSDetails.sort((a, b) => {
        const indexA = levelOrder.indexOf(a.level)
        const indexB = levelOrder.indexOf(b.level)
        if (indexA === -1 && indexB === -1) return 0
        if (indexA === -1) return 1
        if (indexB === -1) return -1
        return indexA - indexB
      })
    },

    // 格式化TS评分
    formatTSScore(score) {
      if (score === null || score === undefined) {
        return '0.000'
      }
      return Number(score).toFixed(3)
    },

    // 获取其他天气要素
    getOtherWeatherElements() {
      if (!this.gradingDetails || !this.gradingDetails.detailResults) {
        return {}
      }

      const detailResults = this.gradingDetails.detailResults
      const otherElements = {}

      // 排除已经单独显示的项目
      const excludeKeys = ['precipitationArea', 'disasterWeather']

      Object.keys(detailResults).forEach(key => {
        if (!excludeKeys.includes(key)) {
          otherElements[key] = detailResults[key]
        }
      })

      return otherElements
    },

    // 获取要素显示名称
    getElementDisplayName(key) {
      const nameMap = {
        'maxTemperature': '最高温度',
        'minTemperature': '最低温度',
        'precipitation': '降水量',
        'temperature': '温度',
        'windForce': '风力',
        'windDirection': '风向'
      }
      return nameMap[key] || key
    }
  }
}
</script>

<style scoped>
.weather-exam-result-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
}

/* 页面头部 */
.result-header {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  color: white;
  padding: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 48px;
  margin-right: 20px;
  opacity: 0.9;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

.back-button .el-button {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.back-button .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 考试信息区域 */
.exam-info-section {
  padding: 30px 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.info-card {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.score-display {
  display: flex;
  align-items: center;
  gap: 40px;
}

.score-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #67c23a, #85ce61);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 25px rgba(103, 194, 58, 0.3);
}

.score-number {
  font-size: 36px;
  font-weight: 700;
  line-height: 1;
}

.score-label {
  font-size: 14px;
  opacity: 0.9;
}

.score-details {
  flex: 1;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 16px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  color: #909399;
  margin-right: 8px;
  min-width: 80px;
}

.detail-item .value {
  color: #303133;
  font-weight: 600;
}

/* 题目区域 */
.question-section {
  padding: 0 40px 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.question-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.question-header h3 {
  margin: 0;
  color: #303133;
  font-size: 20px;
}

.question-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
}

.question-description {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 24px;
}

.forecast-table-section,
.correct-answers-section {
  margin-top: 24px;
}

.forecast-table-section h4,
.correct-answers-section h4 {
  color: #303133;
  font-size: 16px;
  margin-bottom: 16px;
}

.answers-grid {
  display: grid;
  gap: 16px;
}

.answer-item {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.answer-item.correct-answer {
  border-color: #67c23a;
  background: #f0f9ff;
}

.answer-content {
  font-size: 14px;
  color: #303133;
  margin-bottom: 8px;
}

.correct-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #67c23a;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 8px;
}

.answer-analysis {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
}

/* 详细评分信息样式 */
.grading-details-section {
  margin-top: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.grading-details-section h4 {
  color: #303133;
  font-size: 16px;
  margin-bottom: 16px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.precipitation-area-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.section-header h5 {
  margin: 0;
  color: #303133;
  font-size: 15px;
  font-weight: 600;
}

.section-score {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.precipitation-details {
  display: grid;
  gap: 16px;
}

.score-group {
  background: #fafbfc;
  border-radius: 6px;
  padding: 12px;
  border-left: 3px solid #409eff;
}

.score-group h6 {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 13px;
  font-weight: 600;
}

.score-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.score-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  font-size: 15px;
}

.score-label {
  color: #606266;
  font-weight: 500;
  font-size: 15px;
}

.score-value {
  color: #303133;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  font-size: 15px;
}

.summary-section {
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  padding: 12px;
}

.summary-section h6 {
  margin: 0 0 8px 0;
  color: #d48806;
  font-size: 13px;
  font-weight: 600;
}

.summary-content {
  color: #8c6e00;
  font-size: 12px;
  line-height: 1.5;
}

/* TS评分详细统计样式 */
.ts-details-section {
  margin-top: 20px;
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #e1f5fe;
}

.ts-details-section h6 {
  margin: 0 0 15px 0;
  color: #1976d2;
  font-size: 16px;
  font-weight: 600;
}

.level-ts-detail {
  margin-bottom: 25px;
  padding: 15px;
  background-color: #ffffff;
  border-radius: 6px;
  border: 1px solid #e3f2fd;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.level-ts-detail:last-child {
  margin-bottom: 0;
}

.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e8f4fd;
}

.level-header h7 {
  margin: 0;
  color: #1565c0;
  font-size: 15px;
  font-weight: 600;
}

.level-stats {
  display: flex;
  align-items: center;
  gap: 10px;
}

.station-count {
  color: #666;
  font-size: 13px;
  background-color: #f5f5f5;
  padding: 2px 8px;
  border-radius: 12px;
}

.special-rule {
  color: #ff9800;
  cursor: help;
}

.ts-stats-group {
  margin-bottom: 15px;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.ts-stats-group.cma-meso {
  background-color: #fafafa;
  border-color: #d0d0d0;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.stats-title {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.ts-stats-group.cma-meso .stats-title {
  color: #666;
}

.ts-score {
  font-size: 16px;
  font-weight: bold;
  color: #1976d2;
  background-color: #e3f2fd;
  padding: 4px 12px;
  border-radius: 15px;
}

.ts-stats-group.cma-meso .ts-score {
  color: #757575;
  background-color: #f0f0f0;
}

.stats-breakdown {
  display: flex;
  gap: 15px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
}

.stat-item.correct .stat-label {
  color: #4caf50;
  font-weight: 500;
}

.stat-item.correct .stat-value {
  color: #4caf50;
  font-weight: 600;
}

.stat-item.wrong .stat-label {
  color: #ff9800;
  font-weight: 500;
}

.stat-item.wrong .stat-value {
  color: #ff9800;
  font-weight: 600;
}

.stat-item.missed .stat-label {
  color: #f44336;
  font-weight: 500;
}

.stat-item.missed .stat-value {
  color: #f44336;
  font-weight: 600;
}

/* 公式描述和贡献信息样式 */
.formula-description {
  margin-top: 8px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.formula-label {
  font-weight: 500;
  color: #333;
}

.formula-text {
  font-family: 'Courier New', monospace;
  color: #1976d2;
  margin-left: 5px;
}

.contribution-info {
  margin-top: 12px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border-left: 3px solid #2196f3;
}

.contribution-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 13px;
}

.contribution-item:last-child {
  margin-bottom: 0;
}

.contribution-item.total {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #e0e0e0;
  font-weight: 600;
}

.contribution-label {
  color: #666;
}

.contribution-value {
  color: #333;
  font-weight: 500;
}

.contribution-item.total .contribution-label,
.contribution-item.total .contribution-value {
  color: #1976d2;
  font-weight: 600;
}

/* 其他评分项目样式 */
.disaster-weather-section,
.weather-element-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.disaster-details,
.element-details {
  display: grid;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #fafbfc;
  border-radius: 4px;
  border-left: 3px solid #e4e7ed;
  font-size: 13px;
}

.detail-item .label {
  color: #606266;
  font-weight: 500;
  min-width: 80px;
  margin-right: 8px;
}

.detail-item .value {
  color: #303133;
  font-weight: 600;
  flex: 1;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 40px;
  color: #909399;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #606266;
}

.empty-description {
  font-size: 14px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .result-header {
    padding: 20px;
    flex-direction: column;
    gap: 20px;
  }

  .exam-info-section,
  .question-section {
    padding-left: 20px;
    padding-right: 20px;
  }

  .score-display {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .info-card {
    padding: 24px;
  }
}
</style>
